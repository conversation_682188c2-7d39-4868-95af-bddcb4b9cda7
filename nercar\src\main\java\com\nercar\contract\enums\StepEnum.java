package com.nercar.contract.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * @description:
 * @author: 刘紫鑫
 * @Date 2024/11/08 11:08
 */
public enum StepEnum {
    SALES_SAVE("1", "销售公司保存"),
    SALES_SUBMIT("2", "销售公司提交至标准科"),
    STANDARD_RETURN("3", "标准科退回"),
    STANDARD_SUBMIT("4", "标准科提交至技术中心"),
    TECH_RETURN_SALES("5", "技术中心评审人员退回销售"),
    TECH_RETURN_STANDARD("6", "技术中心评审人员退回标准科"),
    TECH_APPROVE("7", "技术中心评审人员通过"),
    STANDARD_APPROVE("8", "标准科复评"),
    REEVALUATION("9", "标准科审核通过"),
    OA("10", "未通过提交至oa"),
    RETURN_PINGSHEN("11", "技术中心-主任副主任通过"),
    TECH_RETURN_UP("12", "技术中心-主任副主任退回至评审人员");
    @EnumValue
    private String code;
    @JsonValue
    private String description;

    StepEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static StepEnum fromCode(String code) {
        for (StepEnum step : StepEnum.values()) {
            if (step.getCode().equals(code)) {
                return step;
            }
        }
        throw new IllegalArgumentException("Invalid step code: " + code);
    }
}