package com.nercar.contract.vo;

import com.nercar.contract.enums.SteelNumerUnitEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @description: 技术中心标准科待审核订单详情
 * @author: zmc
 * @date: 2024/10/14
 */
@Data
public class TechCentStdPendingOrderInfo {
    private String id;
    private String code;
    private String customerName;
    private String customerPhone;
    private String steelTypeName;
    private String steelGradeName;
    private String standardName;
    private String technicalStandardName; // 技术条件名称
    private String specification;
    private String itemName;
    private String value1;
    private String value2;
    private String value3;
    private Byte isHead;
    private Integer steelNumber;
    private SteelNumerUnitEnum steelNumberUnit;
    private String deliveryStatus;
    private String processingPurpose;
    private List<String> smeltingProcess;
    private String specialRequirements;
    private String authorName;
    private String salesmanName;
    private Long attachmentId;
    private Boolean isCostCalculation;
    private Boolean isOutsourcingFirm;
    private Boolean isProduce;
    private Boolean isBack;
    private String remark;
    private String returnReason;
    private String createUser;
    private String updateUser;
    private Long outsourcingId; // 外委业务表
    private BigDecimal outsourcingPrice;
    private String outsourcingName; // 外委名称

    private String outsourcingPhone; // 联系方式
    private Integer recommendRoute; // 推荐路线
    private LocalDateTime submitTime;

    // 科室主任评审意见相关字段
    private Integer receivingState; // 接单状态：0=拒单，1=接单，2=条件接单
    private String receivingStateText; // 接单状态描述
    private String receivingRemark; // 接单备注/条件
    private String costCalculation; // 成本测算
    private Integer assess; // 风险等级评估
    private String assessText; // 风险等级评估描述
    private Integer isMakeReview; // 首试制
    private String isMakeReviewText; // 首试制描述
    private Integer outsourcingStatusReview; // 外协状态
    private Long isCostByChange; // 是否引起成本变化
    private String isCostByChangeText; // 是否引起成本变化描述
}
