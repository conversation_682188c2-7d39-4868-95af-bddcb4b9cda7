package com.nercar.contract.utils;

/**
 * @description:
 * @author: 刘紫鑫
 * @Date 2025/02/20 15:55
 */

import org.apache.poi.xwpf.usermodel.*;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Map;

public class WordTableTextReplacer {
    //遍历docx中的表格 并替换文字
    public static String replaceTextInTable(String inputFilePath, String outputFilePath, Map<String, String> replacements) throws IOException {
        try (FileInputStream fis = new FileInputStream(inputFilePath);
             XWPFDocument document = new XWPFDocument(fis)) {

            // 遍历文档中的段落
            for (XWPFParagraph paragraph : document.getParagraphs()) {
                // 获取段落中的文本

                for (XWPFRun run : paragraph.getRuns()) {


                    String text = run.getText(0);
                    if (text != null && text.contains("id")) {
                        // 替换文本
                        text = text.replace("id", replacements.get("{id}"));
                        run.setText(text, 0);
                    }
                }
                 

            }

            // 遍历文档中的所有表格
            for (XWPFTable table : document.getTables()) {
                for (XWPFTableRow row : table.getRows()) {
                    for (XWPFTableCell cell : row.getTableCells()) {
                        for (XWPFParagraph paragraph : cell.getParagraphs()) {
                            for (XWPFRun run : paragraph.getRuns()) {
                                String text = run.getText(0);
                                if (text != null) {
                                    for (Map.Entry<String, String> entry : replacements.entrySet()) {
                                        text = text.replace(entry.getKey(), entry.getValue());
                                    }
                                    run.setText(text, 0);
                                }
                            }
                        }
                    }
                }
            }

            // 保存修改后的文档
            try (FileOutputStream fos = new FileOutputStream(outputFilePath)) {
                document.write(fos);
            }
        }
        return outputFilePath;
    }
}