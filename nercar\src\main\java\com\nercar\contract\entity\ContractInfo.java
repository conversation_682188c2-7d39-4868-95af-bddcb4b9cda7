package com.nercar.contract.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.nercar.contract.enums.SteelNumerUnitEnum;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

@ApiModel(description = "合同表")
@Data
@Getter
@Setter
public class ContractInfo {

    @TableId(value = "id")
    private Long id; // 主键

    private String code; // 编号

    private Long userId; // 顾客信息表id

    private String steelTypeId; // 钢类id

    private String steelGradeId; // 钢种id

    private Long steelSpecificationId; // 规格id

    private Integer steelNumber; // 数量

    private SteelNumerUnitEnum steelNumberUnit; // 数量单位(0-吨、1-捆、2-支、3-锭、4-Kg)

    private String deliveryStatusId; // 交货状态id

    private String processingPurposeId; // 加工用途id

    private String smeltingProcess; // 冶炼方法

    private String standardId; // 标准id

    private Long technicalStandardId; // 技术条件id

    private Long reviewId; // 评审id

    private String specialRequirements; // 特殊要求

    private String remark; // 备注

    private String salesmanName; // 业务员

    private String authorName; // 填表人

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime; // 创建时间

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime; // 修改时间

    private String createId; // 创建人id

    private String updateId; // 修改人id

    private Integer itemId; // 待处理事项id

    private int statusId; // 当前状态id

    private Integer isHead; // 首评、复评状态

    private Long auditId; // 标准科审核表id

    private Long attachmentId; // pdf的id

    private Long overallOpinionId; // 综合意见表id

    private Long finalOpinionId; // 最终意见表id

    private Integer isSubmit;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime submitTime;

    private Long reviewTypeId;

    private String returnReason;
    @TableField("OUTSOURCING_PRICE")
    private Long outsourcingPrice; // 外委价格
    private String archive="0"; // 存档   0否1是
    private String director; // 分发的主任、副主任
    private Integer recommendRoute; // 推荐路线
    private String type;//类别
    private String submitUser; // 发起人
    // Getters and Setters
    // ...
}